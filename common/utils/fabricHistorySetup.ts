import * as fabric from 'fabric';

// Make fabric available globally for fabric-history
if (typeof window !== 'undefined') {
  (window as any).fabric = fabric;

  // Import fabric-history after fabric is available
  try {
    const fabricHistory = require('fabric-history');
    console.log('fabric-history loaded successfully');

    // Initialize fabric-history on the fabric object
    if (fabricHistory && typeof fabricHistory.init === 'function') {
      fabricHistory.init(fabric);
      console.log('fabric-history initialized');
    }
  } catch (error) {
    console.error('Failed to load fabric-history:', error);
  }
}

export { fabric };
